{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\services\\alarm\\alarmservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:services\\alarm\\alarmservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\services\\devices\\devicebase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:services\\devices\\devicebase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\services\\devices\\idevice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:services\\devices\\idevice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\services\\devices\\kachuanflowpumpdriver.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:services\\devices\\kachuanflowpumpdriver.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\devicesettingswindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:devicesettingswindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\services\\configurationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:services\\configurationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\data\\pemtestdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:data\\pemtestdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\services\\alarm\\alarmlevel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:services\\alarm\\alarmlevel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\models\\control\\experimentstate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:models\\control\\experimentstate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|c:\\users\\<USER>\\nutstore\\1\\project\\3制氢测试系统\\program\\pem电解槽自动化测试系统\\models\\devices\\devicestatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{831DB7D6-27AF-42D4-8050-2A1BF603BBCC}|PEMTestSystem.csproj|solutionrelative:models\\devices\\devicestatus.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 139, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MainWindow.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\MainWindow.xaml.cs", "RelativeToolTip": "MainWindow.xaml.cs", "ViewState": "AgIAAMUAAAAAAAAAAAAiwNUAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T02:54:49.223Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainWindow.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\MainWindow.xaml", "RelativeDocumentMoniker": "MainWindow.xaml", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\MainWindow.xaml", "RelativeToolTip": "MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-06T01:57:38.157Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "DeviceBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Services\\Devices\\DeviceBase.cs", "RelativeDocumentMoniker": "Services\\Devices\\DeviceBase.cs", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Services\\Devices\\DeviceBase.cs", "RelativeToolTip": "Services\\Devices\\DeviceBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAEsAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T13:39:31.538Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "KachuanFlowPumpDriver.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Services\\Devices\\KachuanFlowPumpDriver.cs", "RelativeDocumentMoniker": "Services\\Devices\\KachuanFlowPumpDriver.cs", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Services\\Devices\\KachuanFlowPumpDriver.cs", "RelativeToolTip": "Services\\Devices\\KachuanFlowPumpDriver.cs", "ViewState": "AgIAABwBAAAAAAAAAAAAwCIBAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T06:32:02.144Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "IDevice.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Services\\Devices\\IDevice.cs", "RelativeDocumentMoniker": "Services\\Devices\\IDevice.cs", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Services\\Devices\\IDevice.cs", "RelativeToolTip": "Services\\Devices\\IDevice.cs", "ViewState": "AgIAAPABAAAAAAAAAAAAwPwBAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T06:23:16.933Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "DeviceSettingsWindow.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\DeviceSettingsWindow.xaml", "RelativeDocumentMoniker": "DeviceSettingsWindow.xaml", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\DeviceSettingsWindow.xaml", "RelativeToolTip": "DeviceSettingsWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-28T12:07:55.074Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "AlarmService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Services\\Alarm\\AlarmService.cs", "RelativeDocumentMoniker": "Services\\Alarm\\AlarmService.cs", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Services\\Alarm\\AlarmService.cs", "RelativeToolTip": "Services\\Alarm\\AlarmService.cs", "ViewState": "AgIAAEIAAAAAAAAAAAAAAFUAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T01:17:45.354Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "ConfigurationService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Services\\ConfigurationService.cs", "RelativeDocumentMoniker": "Services\\ConfigurationService.cs", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Services\\ConfigurationService.cs", "RelativeToolTip": "Services\\ConfigurationService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB4AAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T13:52:06.678Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "App.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\App.xaml.cs", "RelativeDocumentMoniker": "App.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\App.xaml.cs", "RelativeToolTip": "App.xaml.cs", "ViewState": "AgIAADEAAAAAAAAAAAAQwFsAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T11:15:29.779Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "App.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\App.xaml", "RelativeDocumentMoniker": "App.xaml", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\App.xaml", "RelativeToolTip": "App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-24T11:15:33.432Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "PEMTestDbContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Data\\PEMTestDbContext.cs", "RelativeDocumentMoniker": "Data\\PEMTestDbContext.cs", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Data\\PEMTestDbContext.cs", "RelativeToolTip": "Data\\PEMTestDbContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T02:33:33.48Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "AlarmLevel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Services\\Alarm\\AlarmLevel.cs", "RelativeDocumentMoniker": "Services\\Alarm\\AlarmLevel.cs", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Services\\Alarm\\AlarmLevel.cs", "RelativeToolTip": "Services\\Alarm\\AlarmLevel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T01:17:39.2Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "ExperimentState.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Models\\Control\\ExperimentState.cs", "RelativeDocumentMoniker": "Models\\Control\\ExperimentState.cs", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Models\\Control\\ExperimentState.cs", "RelativeToolTip": "Models\\Control\\ExperimentState.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAEkAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T03:47:28.308Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "DeviceStatus.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Models\\Devices\\DeviceStatus.cs", "RelativeDocumentMoniker": "Models\\Devices\\DeviceStatus.cs", "ToolTip": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\Models\\Devices\\DeviceStatus.cs", "RelativeToolTip": "Models\\Devices\\DeviceStatus.cs", "ViewState": "AgIAACUAAAAAAAAAAABDwD4AAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T03:46:24.143Z"}]}]}]}